import { Loading, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@components/feedback"
import { Heading } from "@components/common"
import { CartItemList, CartSubtotalPrice } from "@components/eCommerce"
import useCart from "@hooks/useCart"

const Cart = () => {
    const { loading, error, products,userAccessToken , changeQuantityHandler, removeItemHandler } = useCart();

  return (
    <>
        <Heading title="Cart" />
        <Loading loading={loading} error={error} type="cart">
            {products.length ? ( 
                <>
                    <CartItemList products={products} changeQuantityHandler={changeQuantityHandler} removeItemHandler={removeItemHandler}/>
                    <CartSubtotalPrice products={products} userAccessToken={userAccessToken} />
                </>)
            : (<LottieHandler type="empty" message="Your cart is empty" />)    
            }
        </Loading>
    </>
  )
}

export default Cart