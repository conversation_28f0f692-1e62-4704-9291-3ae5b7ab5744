import { Navigate } from "react-router-dom"
import useLogin from "@hooks/useLogin"
import { Input } from "@components/form"
import { Heading } from "@components/common"
import { Al<PERSON>, Button, Col, Form, Row, Spinner } from "react-bootstrap"



const Login = () => {
  const {loading, error, accessToken, message, register, handleSubmit, formErrors, submitForm} = useLogin();
  
  if(accessToken) {
    return <Navigate to={"/"}/>
  }
  
  return (
    <>
    <Heading title="User Login" />
    <Row>
      <Col md={{ span: 6, offset: 3}}>
      {message && <Alert variant="danger">You must login to view this page.</Alert>}
      {message && message === "account_created" && <Alert variant="success">Your account has been created. Please login.</Alert>}
        <Form onSubmit={handleSubmit(submitForm)}>
          <Input name="email" register={register} type="email" error={formErrors.email?.message} label="Email address"/>
          <Input name="password" register={register} type="password" error={formErrors.password?.message} label="Password" />
          <Button variant="info" type="submit" style={{color: "white"}}>
            {loading === "pending" ? ( <> <Spinner animation="border" size="sm"></Spinner> Loading ...</>) : "Submit"}
          </Button>
          {error && ( <p style={{color: "red", marginTop: "10px"}}>{error}</p> )}
        </Form>      
      </Col>
    </Row>
    </>  
    )
}

export default Login