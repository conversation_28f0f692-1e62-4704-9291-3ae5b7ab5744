import type { TProduct } from "@types"
import CartItem from "../CartItem/CartItem"

type CartItemLitsProps = {
    products: TProduct[];
    changeQuantityHandler: (id: number, quantity: number) => void;
    removeItemHandler: (id: number) => void;
}
const CartItemList = ({products, changeQuantityHandler, removeItemHandler}: CartItemLitsProps) => {
    if (!products || products.length === 0) {
        return <div>Your cart is empty</div>
    }
    
    const renderList = products.map((product)=> {
        return <CartItem key={product.id} {...product} changeQuantityHandler={changeQuantityHandler} removeItemHandler={removeItemHandler} />
    })
    
    return (
        <div>{renderList}</div>
    )
}

export default CartItemList
