import { createAsyncThunk } from "@reduxjs/toolkit";
import { axiosErrorHandler } from "@utils";
import axios from "axios";
import { type RootState } from "@store/index";

const actLikeToggle = createAsyncThunk(
    "wishlist/actLikeToggle",
    async(id: number, thunkAPI) => {
        const { rejectWithValue, getState } = thunkAPI;
        const { auth } = getState() as RootState;

        if (!auth.user?.id) {
            return rejectWithValue("Please log in to add items to your wishlist");
        }

        try {
            // Use local Redux state instead of server state to determine action
            const { wishlist } = getState() as RootState;
            const isCurrentlyLiked = wishlist.itemsId.includes(id);

            if(isCurrentlyLiked){
                // Remove from wishlist
                const serverCheck = await axios.get(`/api/wishlist?userId=${auth.user.id}&productId=${id}`);
                if(serverCheck.data.length > 0) {
                    await axios.delete(`/api/wishlist/${serverCheck.data[0].id}`);
                }
                return { type: "remove", id };
            }else{
                // Add to wishlist
                await axios.post(`/api/wishlist`, { userId: auth.user.id, productId: id });
                return { type: "add", id };
            }
        } catch (error) {
            return rejectWithValue(axiosErrorHandler(error))
        }

    }
)

export default actLikeToggle


// import { createAsyncThunk } from "@reduxjs/toolkit";
// import { axiosErrorHandler } from "@utils";
// import axiosInstance from "@utils/axiosInstance";

// const actLikeToggle = createAsyncThunk(
//     "wishlist/actLikeToggle",
//     async(id: number, thunkAPI) => {
//         const { rejectWithValue } = thunkAPI;
//         const userId = "1"; // Match backend's string type for userId

//         try {
//             // First verify if the product exists
//             const isRecordExist = await axiosInstance.get(`/api/wishlist`, {
//                 params: {
//                     userId,
//                     productId: id
//                 }
//             });
//               if(isRecordExist.data.length > 0) {
//                 const wishlistId = isRecordExist.data[0].id;
                
//                 if (!wishlistId) {
//                     return rejectWithValue({
//                         message: "Invalid wishlist item ID",
//                     });
//                 }                
//                 try {
//                     // Use axiosInstance to ensure consistent base URL
//                     await axiosInstance.delete(`/api/wishlist/${wishlistId}`);
//                     return { 
//                         type: "delete", 
//                         id,
//                         message: "Successfully removed from wishlist"
//                     };
//                 } catch (deleteError) {
//                     console.error('Delete failed:', deleteError);
//                     return rejectWithValue({
//                         message: "Failed to remove from wishlist",
//                         error: axiosErrorHandler(deleteError)
//                     });
//                 }            
//             } else {
//                 try {
//                     await axiosInstance.post(`/api/wishlist`, {
//                         userId,
//                         productId: id
//                     });
//                     return { 
//                         type: "add", 
//                         id,
//                         message: "Successfully added to wishlist"
//                     };
//                 } catch (addError) {
//                     console.error('Add failed:', addError);
//                     return rejectWithValue({
//                         message: "Failed to add to wishlist",
//                         error: axiosErrorHandler(addError)
//                     });
//                 }
//             }
//         } catch (error) {
//             console.error('Operation failed:', error);
//             return rejectWithValue({
//                 message: "Operation failed",
//                 error: axiosErrorHandler(error)
//             });
//         }
//     }
// );

// export default actLikeToggle;