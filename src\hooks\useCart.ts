import { useCallback, useEffect } from "react"
import { useAppDispatch, useAppSelector } from "@store/hooks"
import { actGetProductsByItems, cartItemCahngeQuantity, cartItemRemove, cleanCartProductFullInfo } from "@store/cart/cartSlice"

const useCart = () => {
    const dispatch = useAppDispatch();

    const { items, productFullInfo, loading, error} = useAppSelector((state)=> state.cart);
    
    const userAccessToken = useAppSelector((state) => state.auth.accessToken);

    useEffect(()=>{
        const promise = dispatch(actGetProductsByItems());
        return () => {
            promise.abort(); 
            dispatch(cleanCartProductFullInfo());
        };
    }, [dispatch])  

    const changeQuantityHandler = useCallback((id: number, quantity: number) => {
        dispatch(cartItemCahngeQuantity({id, quantity}))
    },[dispatch]);

    const removeItemHandler = useCallback((id: number)=> {
        dispatch(cartItemRemove(id))
    },[dispatch]);


    // Initialize products as empty array if productFullInfo is not an array
    const products = Array.isArray(productFullInfo) 
        ? productFullInfo.map((el)=> ({...el, quantity: items[el.id]}))
        : [];    
  return { loading, error, products,userAccessToken, changeQuantityHandler, removeItemHandler }
}

export default useCart