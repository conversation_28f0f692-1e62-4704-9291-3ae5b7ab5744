import type { TCategory } from '@types';
import { Link } from 'react-router-dom';
import styles from './styles.module.css'
const {category, categoryImg, categoryTitle } = styles;


const Category = ({title, img, prefix}: TCategory) => {
  return (
    <div className={category}>
      <Link to={`/categories/products/${prefix}`}>
        <div className={categoryImg}>
            <img src={img} alt={title} />
        </div>
        <h4 className={categoryTitle}>{title}</h4>
      </Link>
    </div>
  )
}

export default Category

// import type { TCategory } from '@types';
// import { Link } from 'react-router-dom';
// import styles from './styles.module.css'
// const {category, categoryImg, categoryTitle } = styles;

// const Category = ({title, img, prefix}: TCategory) => {
//   // Add this handler function inside the Category component
//   const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
//     const target = e.target as HTMLImageElement;
//     target.src = '/placeholder.jpg'; // Add a placeholder image in your public folder
//   }

//   return (
//     <div className={category}>
//       <Link to={`/categories/products/${prefix}`}>
//         <div className={categoryImg}>
//             <img 
//               src={img} 
//               alt={title} 
//               onError={handleImageError} // Add this line to handle image errors
//             />
//         </div>
//         <h4 className={categoryTitle}>{title}</h4>
//       </Link>
//     </div>
//   )
// }

// export default Category