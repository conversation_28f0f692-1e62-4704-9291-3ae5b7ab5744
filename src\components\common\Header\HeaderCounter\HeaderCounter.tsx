import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import styles from "./styles.module.css"


type HeaderCounterProps = {
  totalQuantity: number,
  svgIcon: React.ReactNode,
  title: string,
  to: string
}

const { container, totalNum, pupmAnimate, iconWrapper } = styles;

const HeaderCounter = ({totalQuantity, svgIcon,title, to}: HeaderCounterProps) => {
  const navigate = useNavigate();
  const [isAnimated, setIsAnimated] = useState(false);
  const quantityStyles = `${totalNum} ${isAnimated ? pupmAnimate : ""}`;
  useEffect(()=>{
    if(!totalQuantity){
      return;
    }
    setIsAnimated(true);
    const debounce = setTimeout(()=>{
      setIsAnimated(false);
    }, 300)
    return () => clearTimeout(debounce);
  },[totalQuantity])
  return (
    <div className={container} onClick={() => navigate(to)}>
      <div className={iconWrapper}>
        {svgIcon}
        {totalQuantity > 0 && <div className={quantityStyles}>{totalQuantity}</div>}
      </div>
      <h3>{title}</h3>
    </div>
  )
}

export default HeaderCounter