import { createSlice } from "@reduxjs/toolkit";
import actLikeToggle from "./act/actLikeToggle";
import actGetWishlist from "./act/actGetWishlist";
import { authLogout } from "@store/auth/authSlice";
import { isString, type TLoading, type TProduct } from "@types";


interface IWishListState {
    itemsId: number[],
    productFullInfo: TProduct[],
    error: null | string,
    loading: TLoading,
    lastLocalUpdate: number, // Timestamp of last local update
}

const initialState: IWishListState = {
    itemsId: [],
    productFullInfo: [],
    error: null,
    loading: "idle",
    lastLocalUpdate: 0,
}

const wishlistSlice = createSlice({
    name: "wishlist",
    initialState,
    reducers: {
        cleanWishlistProductFullInfo: (state) => {
            state.productFullInfo = [];
        },
        resetWishlistError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        // Like toggle handlers
        builder.addCase(actLikeToggle.pending, (state) => {
            state.error = null;
        });
        builder.addCase(actLikeToggle.fulfilled, (state, action) => {
            // Update timestamp to track local changes
            state.lastLocalUpdate = Date.now();

            if(action.payload.type === "add"){
                // Only add if not already present
                if (!state.itemsId.includes(action.payload.id)) {
                    state.itemsId.push(action.payload.id);
                }
            }else{
                state.itemsId = state.itemsId.filter((id) => id !== action.payload.id);
                state.productFullInfo = state.productFullInfo.filter((Product) => Product.id !== action.payload.id);
            }
        });
        builder.addCase(actLikeToggle.rejected, (state, action) => {
            if(isString(action.payload)){
                state.error = action.payload;
            } else {
                state.error = "Failed to update wishlist. Please try again.";
            }
        });
        
        // Get wishlist handlers
        builder.addCase(actGetWishlist.pending, (state) => {
            state.loading = "pending";
            state.error = null;
        });        
        builder.addCase(actGetWishlist.fulfilled, (state, action) => {
            state.loading = "succeeded";
            if(action.payload.dataType === "productFullInfo"){
                state.productFullInfo = action.payload.data as TProduct[];
            } else if(action.payload.dataType === "productIds"){
                state.itemsId = action.payload.data as number[];
            }
        });
        builder.addCase(actGetWishlist.rejected, (state, action) => {
            state.loading = "failed";
            if(isString(action.payload)){
                state.error = action.payload;
            } else {
                state.error = "Failed to load wishlist. Please try again.";
            }
        });
        builder.addCase(authLogout, state => {
            state.itemsId = [];
            state.productFullInfo = [];  
        })
    }
});

export const { cleanWishlistProductFullInfo, resetWishlistError } = wishlistSlice.actions;
export { actLikeToggle, actGetWishlist };
export default wishlistSlice.reducer;