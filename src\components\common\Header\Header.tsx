import { useAppDispatch, useAppSelector } from "@store/hooks";
import { NavLink } from "react-router-dom";
import { Badge, Container, Nav, Navbar, NavDropdown } from "react-bootstrap"
import HeaderLeftBar from "./HeaderLeftBar/HeaderLeftBar";
import { useEffect } from "react";
import { actGetWishlist } from "@store/wishlist/wishlistSlice";

import styles from "./styles.module.css"
import { authLogout } from "@store/auth/authSlice";

const { headerContainer, headerlogo } = styles;
const Header = () => {
const dispatch = useAppDispatch();
const { accessToken, user } = useAppSelector((state) => state.auth)

// Load wishlist only once when user first logs in
useEffect(() => {
    if (accessToken) {
        dispatch(actGetWishlist("productIds"));
    }
}, [dispatch, accessToken]); // Removed 'user' dependency to prevent re-fetching

return (
    <header>
        <div className={headerContainer}>
            <h1 className={headerlogo}>
                <span>Our</span><Badge bg="info">eCom</Badge>
            </h1>
        <HeaderLeftBar />
        </div>
        <Navbar expand="lg" className="bg-body-tertiary" bg="dark" data-bs-theme="dark">
            <Container>
                <Navbar.Toggle aria-controls="basic-navbar-nav" />
                <Navbar.Collapse id="basic-navbar-nav">
                <Nav className="me-auto">
                    <Nav.Link as={NavLink} to="/">Home</Nav.Link>
                    <Nav.Link as={NavLink} to="/categories">Categories</Nav.Link>
                    <Nav.Link as={NavLink} to="/about-us">About</Nav.Link>
                </Nav>
                <Nav>
                {!accessToken ? (
                    <>
                        <Nav.Link as={NavLink} to="/register">Register</Nav.Link>
                        <Nav.Link as={NavLink} to="/login">Login</Nav.Link>
                    </>
                    ) : (
                        <>
                            <NavDropdown title={`Welcome, ${user?.firstName}`} id="basic-nav-dropdown">
                                <NavDropdown.Item as={NavLink} to={"/profile"} >Profile</NavDropdown.Item>
                                <NavDropdown.Item >Orders</NavDropdown.Item>
                                <NavDropdown.Divider />
                                <NavDropdown.Item as={NavLink} to={"/"} onClick={() => dispatch(authLogout())}>Logout</NavDropdown.Item>
                            </NavDropdown>                     
                        </>
                    )}
            </Nav>
                
                </Navbar.Collapse>
            </Container>
        </Navbar>
    </header>
)
}

export default Header