import { GridList, Heading } from "@components/common"
import { Loading } from "@components/feedback";
import { Product } from "@components/eCommerce";
import useWishlist from "@hooks/useWishlist";

const WishList = () => {
    const { loading, error, records } = useWishlist();
    return (
        <>
            <Heading title="WishList" />
            <Loading loading={loading} error={error} type="product">
                <GridList
                records={records}
                renderItem={(records) => <Product {...records} />}
                emptyMessage="Your wishlist is empty"/>
            </Loading>
        </>
    );
};

export default WishList