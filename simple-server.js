const express = require('express');
const cors = require('cors');

console.log('Starting simple server...');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

console.log('Middleware configured');

// Test endpoint
app.get('/auth/test', (req, res) => {
  console.log('Test endpoint hit!');
  res.json({ message: 'Auth endpoint is working!' });
});

// Login endpoint
app.post('/auth/login', (req, res) => {
  console.log('Login endpoint hit with:', req.body);
  const { email, password } = req.body;
  
  // For testing purposes, accept <EMAIL> with password123
  if (email === '<EMAIL>' && password === 'password123') {
    const response = {
      token: 'mock-jwt-token-' + Date.now(),
      user: {
        id: '999',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
      },
    };
    console.log('Sending test user response:', response);
    return res.json(response);
  }
  
  console.log('Invalid credentials');
  res.status(401).json({ message: 'Invalid credentials' });
});

// Register endpoint
app.post('/auth/register', (req, res) => {
  console.log('Register endpoint hit with:', req.body);
  res.status(201).json({ message: 'User created successfully' });
});

// Basic routes for other endpoints
app.get('/users', (req, res) => {
  res.json([]);
});

app.get('/categories', (req, res) => {
  res.json([]);
});

app.get('/products', (req, res) => {
  res.json([]);
});

app.listen(PORT, () => {
  console.log(`Simple Server is running on http://localhost:${PORT}`);
});
