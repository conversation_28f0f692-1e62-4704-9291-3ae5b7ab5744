import { useAppDispatch, useAppSelector } from "@store/hooks"
import { useParams } from "react-router-dom"
import { actGetProductsByCatPrefix, cleanUpProductsRecords } from "@store/products/productsSlice"
import { useEffect } from "react";

const useProducts = () => {
    const params = useParams();
    const productPrefix = params.prefix
    const dispatch = useAppDispatch();
    const { records, loading, error} = useAppSelector((state)=> state.products);
    const cartItems = useAppSelector((state)=> state.cart.items);
    const wishListItemsId = useAppSelector((state) => state.wishlist.itemsId);
    const userAccessToken = useAppSelector((state) => state.auth.accessToken);

    const productFullInfo = records.map((el)=>{
        return {
        ...el,
        quantity: cartItems[el.id],
        isLiked: wishListItemsId.includes(el.id),
        isAuthenticated: userAccessToken ? true : false,
        };
    });
    useEffect(() => {
        const promise = dispatch(actGetProductsByCatPrefix(params.prefix as string));
        return () =>{
        promise.abort();    
        dispatch(cleanUpProductsRecords());
        } 
    },[dispatch, params])
  return { loading, error, productFullInfo, productPrefix}
}

export default useProducts