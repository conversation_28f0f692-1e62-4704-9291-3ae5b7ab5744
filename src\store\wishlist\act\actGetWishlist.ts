import { createAsyncThunk } from "@reduxjs/toolkit"
import axios from "axios"
import type { TProduct } from "@types";
import {axiosErrorHandler} from "@utils";
import { type RootState } from "@store/index";

type TDataType = "productFullInfo" | "productIds";
type TResponse = TProduct[];

const actGetWishlist = createAsyncThunk(
    "wishlist/actGetWishlist",
    async(dataType: TDataType, thunkAPI) => {
        const { rejectWithValue, signal, getState } = thunkAPI;
        const { auth } = getState() as RootState;
        try {
            const userWishlist = await axios.get<{productId: number}[]>(`/api/wishlist?userId=${auth.user?.id}`, {signal});

            // Extract product IDs from wishlist
            const productIds = userWishlist.data.map(item => item.productId);

            if(!userWishlist.data.length) {
                return { data: [], dataType: "empty" };
            }

            if(dataType === "productIds"){
                return { data: productIds, dataType: "productIds" };
            } else {
                const concatenatedItemsId = productIds.map((id) => `id=${id}`).join("&");
                const response = await axios.get<TResponse>(`/api/products?${concatenatedItemsId}`);

                return { data: response.data, dataType: "productFullInfo" };
            }
        } catch(error){
            return rejectWithValue(axiosErrorHandler(error));
        }
    }
)

export default actGetWishlist;

// import { createAsyncThunk } from "@reduxjs/toolkit"
// import type { TProduct } from "@types";
// import {axiosErrorHandler} from "@utils";
// import axiosInstance from "@utils/axiosInstance";

// type TResponse = TProduct[];

// interface WishlistItem {
//     id: number;
//     productId: number;
//     userId: string;
// }

// const actGetWishlist = createAsyncThunk(
//     "wishlist/actGetWishlist",
//     async(_, thunkAPI) => {
//         const { rejectWithValue, signal } = thunkAPI;
//         const userId = "1";  // Match backend's string type for userId

//         try {
//             // First get the wishlist items
//             const userWishlist = await axiosInstance.get<WishlistItem[]>(`/api/wishlist`, {
//                 params: { userId },
//                 signal
//             });
            
            
//             // Extract product IDs and ensure they are unique
//             const productIds = [...new Set(userWishlist.data.map((item: WishlistItem) => item.productId))];
            
//             if(productIds.length === 0) {
//                 return { products: [], itemsId: [] };
//             }
            
//             // Fetch only the products that are in the wishlist
//             const concatenatedItemsId = productIds.map((id) => `id=${id}`).join("&");
//             const response = await axiosInstance.get<TResponse>(`/api/products?${concatenatedItemsId}`);
            
//             return {
//                 products: response.data,
//                 itemsId: productIds
//             };
//         }
//         catch(error){
//             console.error('Wishlist fetch error:', error);
//             return rejectWithValue(axiosErrorHandler(error));
//         }
//     }
// );

// export default actGetWishlist;