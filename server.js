import jsonServer from "json-server";
import bcrypt from "bcryptjs";

const server = jsonServer.create();
const router = jsonServer.router("db.json");
const middlewares = jsonServer.defaults();

// Add body parser FIRST
server.use(jsonServer.bodyParser);

// Add custom routes BEFORE middlewares and router
// Test endpoint
server.get("/auth/test", (req, res) => {
  console.log("Test endpoint hit!");
  res.json({ message: "Auth endpoint is working!" });
});

// Custom login endpoint - MUST be before router
server.post("/auth/login", async (req, res) => {
  console.log("Login endpoint hit with:", req.body);
  const { email, password } = req.body;

  // For testing purposes, accept <EMAIL> with password123
  if (email === "<EMAIL>" && password === "password123") {
    const response = {
      token: "mock-jwt-token-" + Date.now(),
      user: {
        id: "999",
        email: "<EMAIL>",
        firstName: "Test",
        lastName: "User",
      },
    };
    console.log("Sending test user response:", response);
    return res.json(response);
  }

  // For other users, check against database
  try {
    const db = router.db;
    const user = db.get("users").find({ email }).value();

    if (user && (await bcrypt.compare(password, user.password))) {
      const response = {
        token: "mock-jwt-token-" + Date.now(),
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
        },
      };
      console.log("Sending real user response:", response);
      res.json(response);
    } else {
      res.status(401).json({ message: "Invalid password" });
    }
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Custom register endpoint
server.post("/auth/register", async (req, res) => {
  console.log("Register endpoint hit with:", req.body);
  const { firstName, lastName, email, password } = req.body;

  try {
    const db = router.db;

    // Check if user already exists
    const existingUser = db.get("users").find({ email }).value();
    if (existingUser) {
      return res.status(400).json({ message: "User already exists" });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const newUser = {
      id: String(Date.now()),
      firstName,
      lastName,
      email,
      password: hashedPassword,
    };

    db.get("users").push(newUser).write();
    res.status(201).json({ message: "User created successfully" });
  } catch (error) {
    console.error("Register error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Set default middlewares AFTER custom routes
server.use(middlewares);

// Use default router for other routes (this must be LAST)
server.use(router);

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`JSON Server is running on http://localhost:${PORT}`);
});
