.container{
    cursor: pointer;
    align-self: flex-end;
    display: flex;
    align-items: center;
}
.container h3{
    font-size: 15px;
    margin-left: 4px;
    margin-bottom: 0;
}
.iconWrapper{
    position: relative;
}
.totalNum{
    position: absolute;
    height: 20px;
    width: 20px;
    background-color: #0dcaf0;
    border: 1px solid;
    border-radius: 50%;
    top: -11px;
    right: -5px;
    font-size: 12px;
    text-align: center;
}
.pupmAnimate{
    animation: pumping 300ms ease-out;
}
@keyframes pumping {
    0% {
        transform: scale(1);
    }
    20% {
        transform: scale(0.8);
    }
    30% {
        transform: scale(1.1);
    }
    50% {
        transform: scale(1.7);
    }
    100% {
        transform: scale(1);
    }

}