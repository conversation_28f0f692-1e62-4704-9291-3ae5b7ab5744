import { createAsyncThunk } from "@reduxjs/toolkit"
import type { RootState } from "@store/index"
import {axiosErrorHandler} from "@utils";
import axios from "axios"
import type { TProduct } from "@types"

type TResponse = TProduct[];
const actGetProductsByItems = createAsyncThunk(
    "cart/actGetProductsByItems",
    async(_, thunkAPI)=> {
        const { rejectWithValue, fulfillWithValue, getState, signal } = thunkAPI;
        const { cart } = getState() as RootState;
        const itemId = Object.keys(cart.items);
        
        if(!itemId.length){
            return fulfillWithValue([]);
        }        
        try {
            const concatenatedItemsId = itemId.map((el)=> `id=${el}`).join("&");
            const response = await axios.get<TResponse>(`/api/products?${concatenatedItemsId}`, {signal});
            return response.data
        } catch (error) {
            return rejectWithValue(axiosErrorHandler(error));
        }
        
    }    
)

export default actGetProductsByItems
