import actGetProductsByItems from "./act/actGetProductsByItems"
import { getCartTotalQuantitySelector } from "./selectors"
import { type TLoading, type TProduct, isString } from "@types"

import { createSlice } from "@reduxjs/toolkit"

interface ICart {
    items: {[key: string]: number},
    productFullInfo: TProduct[],
    loading: TLoading,
    error: null | string
}

const initialState: ICart = {
    items: {},
    productFullInfo: [], 
    loading: "idle",
    error: null
}

const cartSlice = createSlice({
    name: "cart",
    initialState,    
    reducers: {
        addToCart: (state, action)=>{
            const id = String(action.payload);
            if(state.items[id]){
                state.items[id]++;
            }else{
                state.items[id] = 1;
            }
        },
        // updateQuantity: (state, action) => {
        //     const { id, quantity } = action.payload;
        //     if (quantity === 0) {
        //         delete state.items[String(id)];
        //     } else {
        //         state.items[String(id)] = quantity;
        //     }
        // },
        // removeFromCart: (state, action) => {
        //     delete state.items[String(action.payload)];
        // }
        cartItemCahngeQuantity: (state, action) => {
            state.items[action.payload.id] = action.payload.quantity;
        },
        cartItemRemove: (state, action) => {
            delete state.items[action.payload];
            state.productFullInfo = state.productFullInfo.filter((product) => product.id !== action.payload);
        },
        cleanCartProductFullInfo: (state) => {
            state.productFullInfo = [];
        }
    },
    extraReducers: (builder)=>{
        builder.addCase(actGetProductsByItems.pending, (state)=> {
            state.loading= "pending";
            state.error = null;
        });        builder.addCase(actGetProductsByItems.fulfilled, (state, action)=> {
            state.loading = "succeeded";
            state.productFullInfo = Array.isArray(action.payload) ? action.payload : [];
        });
        builder.addCase(actGetProductsByItems.rejected, (state, action)=> {
            state.loading = "failed";
            if(isString(action.payload)){
                state.error = action.payload;
            }
        })
    }
})




export { getCartTotalQuantitySelector, actGetProductsByItems }
export const { addToCart, cartItemCahngeQuantity, cartItemRemove, cleanCartProductFullInfo } = cartSlice.actions
export default cartSlice.reducer