import { Button } from 'react-bootstrap';

const DevTools = () => {
    const clearStorage = () => {
        localStorage.clear();
        window.location.reload();
    };

    return import.meta.env.MODE === 'development' ? (
        <div style={{ 
            position: 'fixed', 
            bottom: '20px', 
            right: '20px', 
            zIndex: 1000,
            padding: '10px',
            background: '#f8f9fa',
            borderRadius: '5px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
            <Button 
                variant="danger" 
                size="sm"
                onClick={clearStorage}
            >
                Clear Storage & Reload
            </Button>
        </div>
    ) : null;
};

export default DevTools;
