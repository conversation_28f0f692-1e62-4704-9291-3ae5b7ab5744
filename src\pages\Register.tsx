import { Navigate } from "react-router-dom"
import useRegister from "@hooks/useRegister"
import { Heading } from "@components/common"
import { Input } from "@components/form"
import { Button, Col, Form, Row, Spinner } from "react-bootstrap"


const Register = () => {
const { loading, error, accessToken, register, handleSubmit, formErrors, emailOnBlurHandler, submitForm, emailAvailabilityStatus} = useRegister();

if(accessToken) {
  return <Navigate to={"/"}/>
}
  return (
    <>
    <Heading title="User Register" />
    <Row>
      <Col md={{ span: 6, offset: 3}}>
        <Form onSubmit={handleSubmit(submitForm)}>
          <Input label="First Name" name="firstName" register={register} error={formErrors.firstName?.message}/>
          <Input label="Last Name" name="lastName" register={register} error={formErrors.lastName?.message}/>
          <Input label="Email"
          name="email"
          register={register}
          error={formErrors.email?.message
          ? formErrors.email?.message
          : emailAvailabilityStatus === "notAvailable" 
          ? "This email address is already registered"
          : emailAvailabilityStatus === "failed"
          ? "Error from the server. Please try again later"
          : ""  
        } 
          onBlur={emailOnBlurHandler}
          formText={emailAvailabilityStatus === "checking" ? "We are checking the availability of email address. Please wait a moment" : ""}
          success={emailAvailabilityStatus === "available" ? "This email address is available for registration" : ""}
          disabled={emailAvailabilityStatus === "checking" ? true : false}
          />
          <Input label="Password" name="password" type="password" register={register} error={formErrors.password?.message}/>
          <Input label="Confirm Password" name="confirmPassword" type="password" register={register} error={formErrors.confirmPassword?.message}/>
          <Button variant="info" type="submit" style={{color: "white"}}
            disabled={(emailAvailabilityStatus === "checking" ? true : false) || loading === "pending" }>
            {loading === "pending" ? ( <> <Spinner animation="border" size="sm"></Spinner> Loading ...</> ) : "Submit"}
          </Button>
          {error && <p style={{color: "red", marginTop: "10px"}}>{error}</p>}
        </Form>      
      </Col>
    </Row>
    </>
  )
}

export default Register