import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { lazy, Suspense } from 'react';
// Components
import { Lot<PERSON><PERSON>and<PERSON>, PageSuspenseFallback } from '@components/feedback';
// Layouts
const MainLayout = lazy(() => import('../layouts/MainLayout/MainLayout'));
// Pages
const Home = lazy(() => import('@pages/Home'));
const WishList = lazy(() => import('@pages/WishList'));
const Cart = lazy(() => import('@pages/Cart'));
const Products = lazy(() => import('@pages/Products'));
const Categories = lazy(() => import('@pages/Categories'));
const AboutUs = lazy(() => import('@pages/AboutUs'));
const Login = lazy(() => import('@pages/Login'));
const Register = lazy(() => import('@pages/Register'));
const Profile = lazy(() => import('@pages/Profile'));
import Error from '@pages/Error';

// Protected Routes
import ProtectedRoute from '@components/auth/ProtectedRoute';

const router = createBrowserRouter([
  {
    path: "/",
    element:
      <Suspense fallback={
        <div style={{marginTop: "10%"}}>
          <LottieHandler type='loading' message='Loading Please Wait...' />
        </div>
      }>
        <MainLayout />
      </Suspense>,
    errorElement: <Error />,
    children:[
      {
        index: true,
        element: 
          <PageSuspenseFallback>
            <Home />
          </PageSuspenseFallback>
      },{
        path: "/cart",
        element: 
          <PageSuspenseFallback>
            <Cart />
          </PageSuspenseFallback>
      },{
        path: "/wishlist",
        element:
          <ProtectedRoute>
            <PageSuspenseFallback>
              <WishList />
            </PageSuspenseFallback>
          </ProtectedRoute>
      },
      {
        path: "categories/products/:prefix",
        element: 
          <PageSuspenseFallback>
            <Products />
          </PageSuspenseFallback>,
        loader: ({params}) => {
            if(typeof params.prefix !== "string" || !/^[a-z]+$/i.test(params.prefix) ){
                throw new Response("Bad Request", {
                    status: 400,
                    statusText: "Category Not Found"
                })
            }
            return true
        }
      },{
        path: "/categories",
        element: 
          <PageSuspenseFallback>
            <Categories />
          </PageSuspenseFallback>
      },{
        path: "/about-us",
        element: 
          <PageSuspenseFallback>
            <AboutUs />
          </PageSuspenseFallback>
      },{
        path: "login",
        element: 
            <PageSuspenseFallback>
              <Login />
            </PageSuspenseFallback>
      },{
        path: "register",
        element: 
            <PageSuspenseFallback>
              <Register />
            </PageSuspenseFallback>
      },{
        path: "profile",
        element:
          <ProtectedRoute>
            <PageSuspenseFallback>
              <Profile />
            </PageSuspenseFallback>
          </ProtectedRoute>
      }
    ]
  },

])

const AppRouter = () => {
  return (
    <div>  <RouterProvider router={router} /></div>
  )
}

export default AppRouter