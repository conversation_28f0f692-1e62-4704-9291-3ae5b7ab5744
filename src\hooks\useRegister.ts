import { useEffect } from "react"
import { useAppDispatch, useAppSelector } from "@store/hooks"
import { actAuthRegister, resetUI } from "@store/auth/authSlice"
import { useNavigate } from "react-router-dom"
import { useForm, type SubmitHandler } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { signUpSchema, type signUpType  } from "@validations/signUpSchema"
import useCheckEmailAvailability from "@hooks/useCheckEmailAvailability"


const useRegister = () => {
    const dispatch = useAppDispatch();

    const navigate = useNavigate();
  
    const { loading, error, accessToken } = useAppSelector((state) => state.auth)
  
  const { register, handleSubmit, formState: {errors: formErrors}, getFieldState, trigger } = useForm<signUpType>({resolver: zodResolver(signUpSchema), mode: "onBlur"});
  const {checkEmailAvailability, emailAvailabilityStatus, enteredEmail, resetCheckEmailAvailability } = useCheckEmailAvailability();
  const submitForm: SubmitHandler<signUpType> = async (data) => {
    const { firstName, lastName, email, password } = data;
    dispatch(actAuthRegister({ firstName, lastName, email, password })).unwrap().then(() => navigate("/login?message=account_created"));
  }
  const emailOnBlurHandler = async (e : React.FocusEvent<HTMLInputElement>) => {   
    await trigger("email");
    const { isDirty, invalid } = getFieldState("email");
    const value = e.target.value;  
    if(isDirty && !invalid && enteredEmail !== value) {
      checkEmailAvailability(value);
    }
    if(isDirty && invalid && enteredEmail) {
      resetCheckEmailAvailability();
    }
  }
  
  useEffect(() => {
    return () => {
      dispatch(resetUI());
    }
  }, [dispatch]);
  return { loading, error, accessToken, register, handleSubmit, formErrors, emailOnBlurHandler, submitForm, emailAvailabilityStatus}
}

export default useRegister