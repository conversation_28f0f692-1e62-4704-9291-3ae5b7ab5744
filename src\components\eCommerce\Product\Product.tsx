import { useEffect, useState } from "react";
import { useAppDispatch } from "@store/hooks"
import { addToCart } from "@store/cart/cartSlice";
import Like from "@assets/svg/Like.svg?react"
import LikeFilled  from "@assets/svg/like-fill.svg?react"
import { actLikeToggle } from "@store/wishlist/wishlistSlice";
import ProductInfo from "../ProductInfo/ProductInfo";
import { Button, Spinner, Modal } from "react-bootstrap"
import type { TProduct } from "@types"
import styles from "./styles.module.css"

const { maximumNotice, wishlistBtn } = styles;

const Product = ({ title, price, img, id, max, quantity, isLiked, isAuthenticated }: TProduct) => {

  const dispatch = useAppDispatch();

  const [showModal, setShowModal] = useState(false);

  const [isBtnDisabled, setIsBtnDisabled] = useState(false);

  const [isLoading, setIsLoading] = useState(false)

  const currentRemainingQuantity = max - (quantity ?? 0);

  const quantityReachedToMax = currentRemainingQuantity === 0;

  useEffect(()=>{
    if(!isBtnDisabled){
      return;
    }
    setIsBtnDisabled(true);
    const debounce = setTimeout(()=> {
      setIsBtnDisabled(false);
    }, 300);
    return ()=> clearTimeout(debounce);
  },[isBtnDisabled])
  const addToCartHandler = () => {
    dispatch(addToCart(id));
    setIsBtnDisabled(true);
  };

  const toggleLikeHandler = () => {
    if(isAuthenticated){
      if(!isLoading){
        setIsLoading(true);
        dispatch(actLikeToggle(id))
        .unwrap()
        .then(() => {
          setIsLoading(false);
        })
        .catch(()=> {
          setIsLoading(false);
        });
      }
    }else{
      setShowModal(true);
    }
  };
  return (
    <>
      <Modal show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Login Required</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>You must login to add items to your wishlist.</p>
        </Modal.Body>
      </Modal>
      
      <ProductInfo img={img} title={title} price={price}>
        <div className={wishlistBtn} onClick={toggleLikeHandler}>
          {isLoading ? (
            <Spinner animation="border" size="sm" variant="primary" />
          ) : isLiked ? (
            <LikeFilled />
          ) : (
            <Like />
          )}
        </div>
      <p className={maximumNotice}>{quantityReachedToMax ? "Quantity reached To Max" : `You can add: ${currentRemainingQuantity} item(s).`}</p>
      <Button variant="info" style={{color: "white", width: "100%"}} onClick={addToCartHandler} disabled={isBtnDisabled || quantityReachedToMax}>
        {isBtnDisabled ? <><Spinner animation="border" size="sm" />  Loading ...</> : "Add to Cart"}
      </Button>
    </ProductInfo>
    </>
  );
};

export default Product;


// with memo correctly working

// import { useAppDispatch } from "@store/hooks"
// import { addToCart } from "@store/cart/cartSlice";
// import type { TProduct } from "@types"
// import { Button, Spinner } from "react-bootstrap"
// import styles from "./styles.module.css"
// import { memo, useCallback, useEffect, useState } from "react";

// const { product, productImg, maximumNotice } = styles;

// const arePropsEqual = (prevProps: TProduct, nextProps: TProduct) => {
//   return prevProps.id === nextProps.id &&
//     prevProps.price === nextProps.price &&
//     prevProps.title === nextProps.title &&
//     prevProps.img === nextProps.img &&
//     prevProps.max === nextProps.max &&
//     prevProps.quantity === nextProps.quantity;
// };

// const Product = memo(({ title, price, img, id, max, quantity }: TProduct) => {
//   const dispatch = useAppDispatch();
//   const [isBtnDisabled, setIsBtnDisabled] = useState(false);

//   const currentRemainingQuantity = max - (quantity ?? 0);
//   const quantityReachedToMax = currentRemainingQuantity === 0;

//   const enableButton = useCallback(() => {
//     setIsBtnDisabled(false);
//   }, []);

//   useEffect(() => {
//     let timeoutId: ReturnType<typeof setTimeout>;
//     if (isBtnDisabled) {
//       timeoutId = setTimeout(enableButton, 300);
//     }
//     return () => {
//       if (timeoutId) clearTimeout(timeoutId);
//     };
//   }, [isBtnDisabled, enableButton]);

//   const addToCartHandler = useCallback(() => {
//     dispatch(addToCart(id));
//     setIsBtnDisabled(true);
//   }, [dispatch, id]);

//   return (
//     <div className={product}>
//       <div className={productImg}>
//         <img 
//           src={img}
//           alt={title}
//           loading="lazy"
//           style={{ maxWidth: '100%', maxHeight: '100%' }}
//         />
//       </div>
//       <h2>{title}</h2>
//       <h3>{price} Egp</h3>
//       <p className={maximumNotice}>
//         {quantityReachedToMax ? "Quantity reached To Max" : `You can add: ${currentRemainingQuantity} item(s).`}
//       </p>
//       <Button 
//         variant="info" 
//         style={{color: "white"}} 
//         onClick={addToCartHandler} 
//         disabled={isBtnDisabled || quantityReachedToMax}
//       >
//         {isBtnDisabled ? <><Spinner animation="border" size="sm" />  Loading ...</> : "Add to Cart"}
//       </Button>
//     </div>
//   );
// }, arePropsEqual);

// export default Product;
