/* column direction */
.product-column {
    display: flex;
}

.product-column h2 {
    font-size: 16px;
    margin-bottom: 12px;
    width: 100%;
}

.product-column h3 {
    font-size: 13px;
}
.productImg-column img {
    background-color: #f2f2f2;
    display: block;
    height: 180px;
    width: 120px;
    object-fit: contain;
}

.productInfo-column {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    width: 140px;
}

/* row direction */
.product-row {
    width: 130px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    position: relative;
}

.product-row h2 {
    font-size: 15px;
    margin-top: 9px;
    margin-bottom: 12px;
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.product-row h3 {
    font-size: 14px;
}
.productImg-row {
    height: 180px;
    width: 120px;
    background-color: #f2f2f2;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 4px;
}

.productImg-row img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    display: block;
}
.productInfo-row {
    width: 100%;
}