import { useEffect } from "react"
import { useAppDispatch, useAppSelector } from "@store/hooks"
import { actAuthLogin, resetUI } from "@store/auth/authSlice"
import { useSearchParams, useNavigate } from "react-router-dom"
import { useForm, type SubmitHandler } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { signInSchema, type signInType } from "@validations/signInSchema"

const useLogin = () => {
    const navigate = useNavigate();

    const dispatch = useAppDispatch();
  
    const [ searchParams, setSearchParams ] = useSearchParams();
  
    const { loading, error, accessToken } = useAppSelector((state) => state.auth);
  
    const message = searchParams.get("message");
    const { register, handleSubmit, formState: {errors: formErrors}} = useForm<signInType>({resolver: zodResolver(signInSchema), mode: "onBlur"});
    const submitForm: SubmitHandler<signInType> = (data) => {
      if(searchParams.get("message")) {
        setSearchParams("");
      }
      dispatch(actAuthLogin(data))
        .unwrap()
        .then(() => navigate("/"));
    }
    
    useEffect(() => {
      return () => {
        dispatch(resetUI());
      }
    }, [dispatch]);
  return {
    loading, error, accessToken, message, register, handleSubmit, formErrors, submitForm
  }
}

export default useLogin