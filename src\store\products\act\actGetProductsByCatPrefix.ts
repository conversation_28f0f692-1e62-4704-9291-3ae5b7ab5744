import { createAsyncThunk } from "@reduxjs/toolkit";
import Axios from "axios";
import {axiosErrorHandler} from "@utils";
import type { TProduct } from "@types";


type TResponse = TProduct[];
const actGetProductsByCatPrefix = createAsyncThunk(
    "products/actGetProductsByCatPrefix",
    async(prefix: string, thunkAPI)=>{
        const { rejectWithValue, signal } = thunkAPI;
        try {
            const response = await Axios.get<TResponse>(`/api/products?cat_prefix=${prefix}`, { signal});
            return response.data;
        } catch(error){
            return rejectWithValue(axiosErrorHandler(error));
        }
    }
);

export default actGetProductsByCatPrefix;

