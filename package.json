{"name": "ecommerce-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "server": "node simple-server.cjs", "dev:full": "concurrently \"npm run server\" \"npm run dev\""}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@reduxjs/toolkit": "^2.8.2", "@types/react-redux": "^7.1.34", "axios": "^1.9.0", "bootstrap": "^5.3.6", "lottie-react": "^2.4.1", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-content-loader": "^7.0.2", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "redux-persist": "^6.0.0", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/babel__core": "^7.20.5", "@types/node": "^24.0.3", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "bcryptjs": "^2.4.3", "concurrently": "^9.1.0", "cors": "^2.8.5", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "express": "^4.18.2", "globals": "^16.0.0", "json-server": "^0.17.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}