const express = require("express");
const cors = require("cors");
const bcrypt = require("bcryptjs");
const fs = require("fs");

console.log("Starting simple server...");

const app = express();
const PORT = 3001;

// Database functions
const readDB = () => {
  try {
    const data = fs.readFileSync("db.json", "utf8");
    return JSON.parse(data);
  } catch (error) {
    console.error("Error reading database:", error);
    return {
      users: [],
      categories: [],
      products: [],
      wishlist: [],
    };
  }
};

const writeDB = (data) => {
  try {
    fs.writeFileSync("db.json", JSON.stringify(data, null, 2));
  } catch (error) {
    console.error("Error writing database:", error);
  }
};

// Middleware
app.use(cors());
app.use(express.json());

console.log("Middleware configured");

// Test endpoint
app.get("/auth/test", (req, res) => {
  console.log("Test endpoint hit!");
  res.json({ message: "Auth endpoint is working!" });
});

// Login endpoint
app.post("/auth/login", async (req, res) => {
  console.log("Login endpoint hit with:", req.body);
  const { email, password } = req.body;

  try {
    const db = readDB();
    const user = db.users.find((u) => u.email === email);

    if (!user) {
      console.log("User not found:", email);
      return res.status(401).json({ message: "Invalid credentials" });
    }

    // Check password - handle both plain text (test user) and bcrypt hashed passwords
    let passwordMatches = false;
    if (user.password === password) {
      // Plain text password (for test user)
      passwordMatches = true;
    } else {
      // Try bcrypt comparison for hashed passwords
      try {
        passwordMatches = await bcrypt.compare(password, user.password);
      } catch (bcryptError) {
        console.log("Bcrypt comparison failed:", bcryptError.message);
        passwordMatches = false;
      }
    }

    if (passwordMatches) {
      const response = {
        token: "mock-jwt-token-" + Date.now(),
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
        },
      };
      console.log("Sending successful login response for:", email);
      return res.json(response);
    } else {
      console.log("Invalid password for user:", email);
      return res.status(401).json({ message: "Invalid password" });
    }
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Register endpoint
app.post("/auth/register", async (req, res) => {
  console.log("Register endpoint hit with:", req.body);
  const { firstName, lastName, email, password } = req.body;

  try {
    const db = readDB();

    // Check if user already exists
    const existingUser = db.users.find((u) => u.email === email);
    if (existingUser) {
      return res.status(400).json({ message: "User already exists" });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const newUser = {
      id: String(Date.now()),
      firstName,
      lastName,
      email,
      password: hashedPassword,
    };

    db.users.push(newUser);
    writeDB(db);

    console.log("User created successfully:", email);
    res.status(201).json({ message: "User created successfully" });
  } catch (error) {
    console.error("Register error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Basic routes for other endpoints
app.get("/users", (req, res) => {
  const db = readDB();
  res.json(db.users || []);
});

app.get("/categories", (req, res) => {
  const db = readDB();
  // Handle both "categories" and "category" property names
  const categories = db.categories || db.category || [];
  console.log(
    "Categories endpoint hit, returning:",
    categories.length,
    "categories"
  );
  res.json(categories);
});

app.get("/products", (req, res) => {
  const db = readDB();
  let products = db.products || [];

  // Filter by cat_prefix if provided
  const { cat_prefix, id } = req.query;
  if (cat_prefix) {
    products = products.filter((product) => product.cat_prefix === cat_prefix);
    console.log(
      `Products endpoint hit with cat_prefix=${cat_prefix}, returning:`,
      products.length,
      "products"
    );
  } else if (id) {
    // Handle multiple IDs for wishlist (id=1&id=2&id=3)
    const ids = Array.isArray(id) ? id : [id];
    products = products.filter((product) => ids.includes(String(product.id)));
    console.log(
      `Products endpoint hit with ids=${ids.join(",")}, returning:`,
      products.length,
      "products"
    );
  } else {
    console.log(
      "Products endpoint hit, returning all:",
      products.length,
      "products"
    );
  }

  res.json(products);
});

// Wishlist endpoints
app.get("/wishlist", (req, res) => {
  const db = readDB();
  const { userId, productId } = req.query;
  let wishlist = db.wishlist || [];

  if (userId && productId) {
    wishlist = wishlist.filter(
      (item) => item.userId === userId && item.productId === parseInt(productId)
    );
  } else if (userId) {
    wishlist = wishlist.filter((item) => item.userId === userId);
  }

  console.log(
    `Wishlist GET: userId=${userId}, productId=${productId}, returning:`,
    wishlist.length,
    "items"
  );
  res.json(wishlist);
});

app.post("/wishlist", (req, res) => {
  const { userId, productId } = req.body;
  const db = readDB();

  if (!db.wishlist) {
    db.wishlist = [];
  }

  // Check if item already exists
  const existingItem = db.wishlist.find(
    (item) => item.userId === userId && item.productId === productId
  );
  if (existingItem) {
    return res.status(400).json({ message: "Item already in wishlist" });
  }

  const newItem = {
    id: Date.now(),
    userId,
    productId: parseInt(productId),
  };

  db.wishlist.push(newItem);
  writeDB(db);

  console.log(
    `Wishlist POST: Added item for userId=${userId}, productId=${productId}`
  );
  res.status(201).json(newItem);
});

app.delete("/wishlist/:id", (req, res) => {
  const { id } = req.params;
  const db = readDB();

  if (!db.wishlist) {
    return res.status(404).json({ message: "Wishlist item not found" });
  }

  const itemIndex = db.wishlist.findIndex((item) => item.id === parseInt(id));
  if (itemIndex === -1) {
    return res.status(404).json({ message: "Wishlist item not found" });
  }

  db.wishlist.splice(itemIndex, 1);
  writeDB(db);

  console.log(`Wishlist DELETE: Removed item with id=${id}`);
  res.json({ message: "Item removed from wishlist" });
});

app.listen(PORT, () => {
  console.log(`Simple Server is running on http://localhost:${PORT}`);
});
