import express from "express";
import cors from "cors";
import bcrypt from "bcryptjs";
import fs from "fs";

console.log("Starting auth server...");

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Read database
const readDB = () => {
  try {
    const data = fs.readFileSync("db.json", "utf8");
    return JSON.parse(data);
  } catch (error) {
    console.error("Error reading database:", error);
    return { users: [] };
  }
};

// Write database
const writeDB = (data) => {
  try {
    fs.writeFileSync("db.json", JSON.stringify(data, null, 2));
  } catch (error) {
    console.error("Error writing database:", error);
  }
};

// Test endpoint
app.get("/auth/test", (req, res) => {
  console.log("Test endpoint hit!");
  res.json({ message: "Auth endpoint is working!" });
});

// Login endpoint
app.post("/auth/login", async (req, res) => {
  console.log("Login endpoint hit with:", req.body);
  const { email, password } = req.body;

  try {
    // For testing purposes, accept <EMAIL> with password123
    if (email === "<EMAIL>" && password === "password123") {
      const response = {
        token: "mock-jwt-token-" + Date.now(),
        user: {
          id: "999",
          email: "<EMAIL>",
          firstName: "Test",
          lastName: "User",
        },
      };
      return res.json(response);
    }

    // For other users, check against database
    const db = readDB();
    const user = db.users.find((u) => u.email === email);

    if (user && (await bcrypt.compare(password, user.password))) {
      const response = {
        token: "mock-jwt-token-" + Date.now(),
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
        },
      };
      console.log("Sending real user response:", response);
      res.json(response);
    } else {
      res.status(401).json({ message: "Invalid password" });
    }
  } catch (error) {
    res.status(500).json({ message: "Server error" });
  }
});

// Register endpoint
app.post("/auth/register", async (req, res) => {
  console.log("Register endpoint hit with:", req.body);
  const { firstName, lastName, email, password } = req.body;

  try {
    const db = readDB();

    // Check if user already exists
    const existingUser = db.users.find((u) => u.email === email);
    if (existingUser) {
      return res.status(400).json({ message: "User already exists" });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const newUser = {
      id: String(Date.now()),
      firstName,
      lastName,
      email,
      password: hashedPassword,
    };

    db.users.push(newUser);
    writeDB(db);

    res.status(201).json({ message: "User created successfully" });
  } catch (error) {
    console.error("Register error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Wishlist POST endpoint
app.post("/wishlist", (req, res) => {
  console.log("Wishlist POST endpoint hit with:", req.body);
  const { userId, productId } = req.body;

  try {
    const db = readDB();

    if (!db.wishlist) {
      db.wishlist = [];
    }

    // Check if item already exists
    const existingItem = db.wishlist.find(
      (item) => item.userId == userId && item.productId == productId
    );

    if (existingItem) {
      return res.status(400).json({ message: "Item already in wishlist" });
    }

    // Create new wishlist item
    const newItem = {
      id: Date.now(),
      userId: userId,
      productId: parseInt(productId),
    };

    db.wishlist.push(newItem);
    writeDB(db);

    console.log(`Added to wishlist: userId=${userId}, productId=${productId}`);
    res.status(201).json(newItem);
  } catch (error) {
    console.error("Wishlist POST error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Wishlist DELETE endpoint
app.delete("/wishlist/:id", (req, res) => {
  console.log("Wishlist DELETE endpoint hit with id:", req.params.id);
  const { id } = req.params;

  try {
    const db = readDB();

    if (!db.wishlist) {
      return res.status(404).json({ message: "Wishlist item not found" });
    }

    const itemIndex = db.wishlist.findIndex((item) => item.id == id);

    if (itemIndex === -1) {
      return res.status(404).json({ message: "Wishlist item not found" });
    }

    db.wishlist.splice(itemIndex, 1);
    writeDB(db);

    console.log(`Removed from wishlist: id=${id}`);
    res.json({ message: "Item removed from wishlist" });
  } catch (error) {
    console.error("Wishlist DELETE error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Proxy other requests to JSON Server (for categories, products, etc.)
app.use("/", (req, res) => {
  // For now, just return the database content for other routes
  const db = readDB();

  if (req.path === "/users" && req.method === "GET") {
    res.json(db.users);
  } else if (req.path === "/categories" && req.method === "GET") {
    res.json(db.categories || []);
  } else if (req.path === "/products" && req.method === "GET") {
    const { id, cat_prefix } = req.query;
    let products = db.products || [];

    if (cat_prefix) {
      // Filter products by category prefix
      products = products.filter(
        (product) => product.cat_prefix === cat_prefix
      );
      console.log(
        `Products filtered by cat_prefix=${cat_prefix}, returning ${products.length} products`
      );
    } else if (id) {
      // Handle multiple id parameters (e.g., ?id=1&id=2&id=3)
      const ids = Array.isArray(id) ? id : [id];
      const numericIds = ids.map((i) => parseInt(i)).filter((i) => !isNaN(i));
      products = products.filter((product) => numericIds.includes(product.id));
      console.log(
        `Products filtered by ids=${numericIds.join(",")}, returning ${
          products.length
        } products`
      );
    } else {
      console.log(`Returning all ${products.length} products`);
    }

    res.json(products);
  } else if (req.path === "/wishlist" && req.method === "GET") {
    const userId = req.query.userId;
    if (userId) {
      // Filter wishlist by userId, handling both string and number types
      const userWishlist = (db.wishlist || []).filter(
        (item) => item.userId == userId // Use == for loose comparison to handle string/number mismatch
      );
      res.json(userWishlist);
    } else {
      res.json(db.wishlist || []);
    }
  } else if (req.path === "/cart" && req.method === "GET") {
    res.json(db.cart || []);
  } else {
    res.status(404).json({ message: "Not found" });
  }
});

app.listen(PORT, "0.0.0.0", () => {
  console.log(`Auth Server is running on http://localhost:${PORT}`);
});
