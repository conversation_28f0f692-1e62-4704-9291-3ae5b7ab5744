import { PersistGate } from 'redux-persist/integration/react'
// import { persistor } from '@store/index';
import { createRoot } from 'react-dom/client'
import AppRouter from '@routes/AppRouter'
//axios
import './services/axios-global.js';
//redux
import { Provider } from 'react-redux';
import {store, persistor }  from '@store';


import 'bootstrap/dist/css/bootstrap.min.css';
import '@styles/global.css';


createRoot(document.getElementById('root')!).render(
  <Provider store={store}>
    <PersistGate loading={null} persistor={persistor}>
      <AppRouter />
    </PersistGate>
  </Provider>
) 


