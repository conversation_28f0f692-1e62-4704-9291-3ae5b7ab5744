import { useAppSelector } from "@store/hooks";
import { getCartTotalQuantitySelector } from "@store/cart/selectors";
import WishlistIcon from "@assets/svg/wishlist.svg?react"
import CartIcon from "@assets/svg/cart.svg?react"
import HeaderCounter from "../HeaderCounter/HeaderCounter";

import styles from "./styles.module.css"
const { headerLeftBar } = styles


const HeaderLeftBar = () => {
    const wishlistTotalQuantity = useAppSelector((state) => state.wishlist.itemsId.length);
    const cartTotalQuantity = useAppSelector(getCartTotalQuantitySelector);
  return (
        <div className={headerLeftBar}>
            <HeaderCounter to="wishlist" totalQuantity={wishlistTotalQuantity} svgIcon={<WishlistIcon title="wishlist icon" />} title="WishList"/>
            <HeaderCounter to="cart" totalQuantity={cartTotalQuantity} svgIcon={<CartIcon title="cart icon" />} title="Cart" />
        </div>
  )
}

export default HeaderLeftBar