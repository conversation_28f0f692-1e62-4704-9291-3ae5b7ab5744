import type { TProduct } from "@types";
import styles  from "./styles.module.css"
import { Button } from "react-bootstrap";

type CartSubtotalPriceProps = {
  products: TProduct[],
  userAccessToken: string | null
};

const CartSubtotalPrice = ({products, userAccessToken}: CartSubtotalPriceProps) => {
  const subtotal = products.reduce((accumulator, product)=> {
    const quantity = product.quantity;
    const price = product.price;
    if(quantity && typeof quantity === "number"){
      return accumulator + (price * quantity);
    }else{
      return accumulator;
    }
  },0) 

  return (
    <>
      <div className={styles.container}>
          <span>Subtotal:</span>
          <span>{subtotal.toFixed(2)} EGP</span>
      </div>
      {
        userAccessToken && (
        <div className={styles.container}>
          <span> </span>
          <span><Button variant="info" style={{color: "white"}}>Place Order</Button></span>
        </div> )
      }
    </>
  )
}

export default CartSubtotalPrice