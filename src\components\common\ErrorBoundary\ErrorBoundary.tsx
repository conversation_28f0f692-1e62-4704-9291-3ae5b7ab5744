import React, { Component, type ReactNode } from 'react';
import { Container } from 'react-bootstrap';
import { <PERSON><PERSON><PERSON>and<PERSON> } from '@components/feedback';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Container>
          <div className="d-flex flex-column align-items-center" style={{ marginTop: "15%" }}>
            <LottieHandler type="error" message="Something went wrong. Please refresh the page." />
            <button 
              className="btn btn-primary mt-3"
              onClick={() => window.location.reload()}
            >
              Refresh Page
            </button>
          </div>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
