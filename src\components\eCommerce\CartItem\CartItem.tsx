import { memo } from "react";
import ProductInfo from "../ProductInfo/ProductInfo";
import { Button, Form } from "react-bootstrap"
import type { TProduct } from "@types";
import styles  from "./styles.module.css"
import type React from "react";



const {cartItem, CartItemSelection} = styles;
type CartItemProps = TProduct & { changeQuantityHandler: (id: number, quantity: number) => void, removeItemHandler: (id: number) => void };

const CartItem = memo(({id, title, price, img, max, quantity, changeQuantityHandler, removeItemHandler }: CartItemProps) => {
        
    //render option list
    const renderOption = Array(max).fill(0).map((_, idx)=> {
        const quantity = idx + 1;
        return (
            <option value={quantity} key={quantity}>{quantity}</option>
        )
    })

    const changeQuantity = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const quantity = +event.target.value;
        changeQuantityHandler(id, quantity);
    }
return (
    <div className={cartItem}>
        <ProductInfo title={title} price={price} img={img} direction="column">
                <Button variant="secondary" style={{color: "white", width: "100px"}} className="mt-auto" onClick={() => removeItemHandler(id)}>Remove</Button>
        </ProductInfo>
        <div className={CartItemSelection}>
            <span className="mb-1 d-block">Quantity</span>
            <Form.Select value={quantity} onChange={changeQuantity}>
                {renderOption}
            </Form.Select>
        </div>
    </div>
    )
});

export default CartItem