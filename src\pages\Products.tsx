import { Product } from "@components/eCommerce"
import { Loading } from "@components/feedback"
import { GridList, Heading } from "@components/common"
import type { TProduct } from "@types";
import useProducts from "@hooks/useProducts";

function Products() {
  const { loading, error, productFullInfo, productPrefix } = useProducts();

  return (
    <>
      <Heading title={`${productPrefix?.toUpperCase()} Products`} />
      <Loading loading={loading} error={error} type="product">
        <GridList<TProduct>
        records={productFullInfo}
        renderItem={(record)=> <Product {...record} />}
        emptyMessage="There are no products"/>
      </Loading>
    </>
  )
}

export default Products
