import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";  
import {axiosErrorHandler} from "@utils";
import type { TCategory } from "@types";

type TResponse = TCategory[];
const actGetCategories = createAsyncThunk(
    "categories/getCategories",
    async(_, thunkAPI)=>{  
        const { rejectWithValue, signal } =thunkAPI;
        try {            
            const response = await axios.get<TResponse>(`/api/categories`, {signal});  
            return response.data;
        } catch(error){
            return rejectWithValue(axiosErrorHandler(error));
        }
    }
);

export default actGetCategories;